#!/usr/bin/env python3
"""
Test different logo positions
"""

from watermark_tool import WatermarkTool
from pathlib import Path
import os

def test_all_positions():
    """Test watermark in all positions"""
    if not os.path.exists("logo.png"):
        print("Error: logo.png not found!")
        return
        
    if not os.path.exists("sample1.jpg"):
        print("Error: sample1.jpg not found!")
        return
    
    tool = WatermarkTool("logo.png")
    
    positions = [
        'top-left', 'top-center', 'top-right',
        'center-left', 'center', 'center-right',
        'bottom-left', 'bottom-center', 'bottom-right'
    ]
    
    # Create test output folder
    test_folder = Path("position_test")
    test_folder.mkdir(exist_ok=True)
    
    print("Testing all logo positions...")
    
    for position in positions:
        output_file = test_folder / f"sample1_{position}.jpg"
        print(f"Creating: {output_file.name}")
        
        success = tool.add_watermark(
            "sample1.jpg", 
            str(output_file),
            opacity=0.8,
            position=position
        )
        
        if success:
            print(f"  ✅ Success: {position}")
        else:
            print(f"  ❌ Failed: {position}")
    
    print(f"\nTest completed! Check the '{test_folder}' folder for results.")

if __name__ == "__main__":
    test_all_positions()
