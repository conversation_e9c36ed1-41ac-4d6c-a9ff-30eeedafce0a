#!/usr/bin/env python3
"""
Create sample images for testing the watermark tool
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_sample_image(filename, size=(800, 600), color=(70, 130, 180), text="Sample Image"):
    """Create a sample image with text"""
    image = Image.new('RGB', size, color)
    draw = ImageDraw.Draw(image)
    
    # Try to use a default font, fallback to basic if not available
    try:
        font = ImageFont.truetype("arial.ttf", 48)
    except:
        font = ImageFont.load_default()
    
    # Calculate text position (center)
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    # Draw text
    draw.text((x, y), text, fill='white', font=font)
    
    # Add some decorative elements
    draw.rectangle([50, 50, size[0]-50, size[1]-50], outline='white', width=3)
    
    # Save image
    image.save(filename, quality=95)
    print(f"Created: {filename}")

def main():
    """Create sample images for testing"""
    print("Creating sample images for testing...")
    
    # Create different sized sample images
    samples = [
        ("sample1.jpg", (800, 600), (70, 130, 180), "Sample Photo 1"),
        ("sample2.jpg", (1200, 800), (220, 20, 60), "Sample Photo 2"),
        ("sample3.png", (600, 800), (34, 139, 34), "Sample Photo 3"),
        ("sample4.jpg", (1000, 600), (138, 43, 226), "Sample Photo 4"),
    ]
    
    for filename, size, color, text in samples:
        create_sample_image(filename, size, color, text)
    
    print(f"\nCreated {len(samples)} sample images!")
    print("You can now test the watermark tool with these images.")

if __name__ == "__main__":
    main()
