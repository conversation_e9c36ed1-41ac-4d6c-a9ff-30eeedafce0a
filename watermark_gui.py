#!/usr/bin/env python3
"""
Simple GUI for Image Watermark Tool
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import threading
from pathlib import Path
from watermark_tool import WatermarkTool

class WatermarkGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Image Watermark Tool")
        self.root.geometry("500x400")
        
        # Variables
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.logo_path = tk.StringVar(value="logo.png")
        
        self.setup_ui()
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Image Watermark Tool", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Input folder selection
        ttk.Label(main_frame, text="Thư mục ảnh:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.input_folder, width=40).grid(row=1, column=1, padx=5)
        ttk.Button(main_frame, text="Chọn", command=self.select_input_folder).grid(row=1, column=2)
        
        # Logo file selection
        ttk.Label(main_frame, text="File logo:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.logo_path, width=40).grid(row=2, column=1, padx=5)
        ttk.Button(main_frame, text="Chọn", command=self.select_logo).grid(row=2, column=2)
        
        # Output folder selection
        ttk.Label(main_frame, text="Thư mục lưu:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.output_folder, width=40).grid(row=3, column=1, padx=5)
        ttk.Button(main_frame, text="Chọn", command=self.select_output_folder).grid(row=3, column=2)
        
        # Process button
        self.process_btn = ttk.Button(main_frame, text="Xử lý ảnh", 
                                     command=self.process_images, style="Accent.TButton")
        self.process_btn.grid(row=4, column=0, columnspan=3, pady=20)
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # Status text
        self.status_text = tk.Text(main_frame, height=10, width=60)
        self.status_text.grid(row=6, column=0, columnspan=3, pady=10, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbar for status text
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=self.status_text.yview)
        scrollbar.grid(row=6, column=3, sticky=(tk.N, tk.S))
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        # Configure grid weights
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(6, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # Initial status
        self.log("Sẵn sàng xử lý ảnh!")
        self.log("1. Chọn thư mục chứa ảnh")
        self.log("2. Chọn file logo (mặc định: logo.png)")
        self.log("3. Chọn thư mục lưu kết quả (tùy chọn)")
        self.log("4. Nhấn 'Xử lý ảnh'")
        
    def log(self, message):
        """Add message to status text"""
        self.status_text.insert(tk.END, message + "\n")
        self.status_text.see(tk.END)
        self.root.update_idletasks()
        
    def select_input_folder(self):
        folder = filedialog.askdirectory(title="Chọn thư mục chứa ảnh")
        if folder:
            self.input_folder.set(folder)
            self.log(f"Đã chọn thư mục ảnh: {folder}")
            
    def select_logo(self):
        file = filedialog.askopenfilename(
            title="Chọn file logo",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.bmp *.tiff")]
        )
        if file:
            self.logo_path.set(file)
            self.log(f"Đã chọn logo: {file}")
            
    def select_output_folder(self):
        folder = filedialog.askdirectory(title="Chọn thư mục lưu kết quả")
        if folder:
            self.output_folder.set(folder)
            self.log(f"Đã chọn thư mục lưu: {folder}")
            
    def process_images(self):
        """Process images in a separate thread"""
        if not self.input_folder.get():
            messagebox.showerror("Lỗi", "Vui lòng chọn thư mục chứa ảnh!")
            return
            
        if not os.path.exists(self.logo_path.get()):
            messagebox.showerror("Lỗi", f"File logo không tồn tại: {self.logo_path.get()}")
            return
            
        # Disable button and start progress
        self.process_btn.config(state="disabled")
        self.progress.start()
        
        # Run in separate thread
        thread = threading.Thread(target=self._process_worker)
        thread.daemon = True
        thread.start()
        
    def _process_worker(self):
        """Worker thread for processing images"""
        try:
            self.log("\n" + "="*50)
            self.log("Bắt đầu xử lý ảnh...")
            
            # Create watermark tool
            tool = WatermarkTool(self.logo_path.get())
            
            # Determine output folder
            output_folder = self.output_folder.get()
            if not output_folder:
                output_folder = Path(self.input_folder.get()) / "watermarked"
            
            # Process images
            input_path = Path(self.input_folder.get())
            
            # Find all image files
            image_files = []
            for ext in tool.supported_formats:
                image_files.extend(input_path.glob(f"*{ext}"))
                image_files.extend(input_path.glob(f"*{ext.upper()}"))
            
            if not image_files:
                self.log("Không tìm thấy ảnh nào để xử lý!")
                return
                
            self.log(f"Tìm thấy {len(image_files)} ảnh để xử lý...")
            
            # Create output folder
            Path(output_folder).mkdir(exist_ok=True)
            
            success_count = 0
            for i, image_file in enumerate(image_files, 1):
                output_file = Path(output_folder) / f"{image_file.stem}_watermarked{image_file.suffix}"
                
                self.log(f"[{i}/{len(image_files)}] Xử lý: {image_file.name}")
                
                if tool.add_watermark(str(image_file), str(output_file)):
                    success_count += 1
                else:
                    self.log(f"  ❌ Lỗi xử lý: {image_file.name}")
                    
            self.log(f"\n✅ Hoàn thành! Đã xử lý {success_count}/{len(image_files)} ảnh")
            self.log(f"📁 Kết quả lưu tại: {output_folder}")
            
            # Show completion message
            self.root.after(0, lambda: messagebox.showinfo(
                "Hoàn thành", 
                f"Đã xử lý {success_count}/{len(image_files)} ảnh thành công!\n\nKết quả lưu tại:\n{output_folder}"
            ))
            
        except Exception as e:
            self.log(f"❌ Lỗi: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("Lỗi", f"Có lỗi xảy ra: {str(e)}"))
            
        finally:
            # Re-enable button and stop progress
            self.root.after(0, self._finish_processing)
            
    def _finish_processing(self):
        """Finish processing - re-enable UI"""
        self.progress.stop()
        self.process_btn.config(state="normal")

def main():
    root = tk.Tk()
    app = WatermarkGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
