#!/usr/bin/env python3
"""
Simple GUI for Image Watermark Tool
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import threading
from pathlib import Path
from watermark_tool import WatermarkTool
from PIL import Image, ImageTk

class WatermarkGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🖼️ Image Watermark Tool - Công cụ chèn logo vào ảnh")
        self.root.geometry("700x600")
        self.root.minsize(650, 550)

        # Configure style
        self.setup_styles()

        # Variables
        self.selected_images = []  # List of selected image files
        self.output_folder = tk.StringVar()
        self.logo_path = tk.StringVar(value="logo.png")
        self.position = tk.StringVar(value="bottom-right")
        self.opacity = tk.DoubleVar(value=0.7)
        self.logo_size = tk.DoubleVar(value=0.15)

        self.setup_ui()

    def setup_styles(self):
        """Setup custom styles"""
        style = ttk.Style()

        # Configure button styles
        style.configure("Title.TLabel", font=("Arial", 18, "bold"), foreground="#2c3e50")
        style.configure("Heading.TLabel", font=("Arial", 11, "bold"), foreground="#34495e")
        style.configure("Process.TButton", font=("Arial", 12, "bold"))

        # Configure colors
        self.root.configure(bg="#ecf0f1")
        
    def setup_ui(self):
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Main tab
        main_tab = ttk.Frame(notebook)
        notebook.add(main_tab, text="🖼️ Xử lý ảnh")

        # Settings tab
        settings_tab = ttk.Frame(notebook)
        notebook.add(settings_tab, text="⚙️ Cài đặt")

        self.setup_main_tab(main_tab)
        self.setup_settings_tab(settings_tab)

    def setup_main_tab(self, parent):
        # Main frame with padding
        main_frame = ttk.Frame(parent, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="🖼️ Công cụ chèn logo vào ảnh",
                               style="Title.TLabel")
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 30))

        # File selection section
        file_frame = ttk.LabelFrame(main_frame, text="📁 Chọn file", padding="15")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))
        file_frame.columnconfigure(1, weight=1)

        # Image files selection
        ttk.Label(file_frame, text="Ảnh cần xử lý:", style="Heading.TLabel").grid(row=0, column=0, sticky=tk.W, pady=8)

        # Frame for image selection
        image_select_frame = ttk.Frame(file_frame)
        image_select_frame.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), padx=(10, 0))
        image_select_frame.columnconfigure(0, weight=1)

        # Buttons for image selection
        button_frame = ttk.Frame(image_select_frame)
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))

        ttk.Button(button_frame, text="🖼️ Chọn ảnh", command=self.select_images).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="📂 Chọn thư mục", command=self.select_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="🗑️ Xóa hết", command=self.clear_images).pack(side=tk.LEFT)

        # Selected images display
        self.images_text = tk.Text(image_select_frame, height=4, width=60, wrap=tk.WORD,
                                  font=("Consolas", 8), bg="#f8f9fa", fg="#2c3e50")
        self.images_text.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

        # Scrollbar for images text
        images_scrollbar = ttk.Scrollbar(image_select_frame, orient="vertical", command=self.images_text.yview)
        images_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.images_text.configure(yscrollcommand=images_scrollbar.set)

        # Logo file selection
        ttk.Label(file_frame, text="File logo:", style="Heading.TLabel").grid(row=2, column=0, sticky=tk.W, pady=8)
        ttk.Entry(file_frame, textvariable=self.logo_path, width=50).grid(row=2, column=1, padx=(10, 5), sticky=(tk.W, tk.E))
        ttk.Button(file_frame, text="🖼️ Chọn", command=self.select_logo).grid(row=2, column=2, padx=(5, 0))

        # Output folder selection
        ttk.Label(file_frame, text="Thư mục lưu:", style="Heading.TLabel").grid(row=3, column=0, sticky=tk.W, pady=8)
        ttk.Entry(file_frame, textvariable=self.output_folder, width=50).grid(row=3, column=1, padx=(10, 5), sticky=(tk.W, tk.E))
        ttk.Button(file_frame, text="💾 Chọn", command=self.select_output_folder).grid(row=3, column=2, padx=(5, 0))

        # Quick settings section
        quick_frame = ttk.LabelFrame(main_frame, text="⚡ Cài đặt nhanh", padding="15")
        quick_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))
        quick_frame.columnconfigure(1, weight=1)

        # Position selection
        ttk.Label(quick_frame, text="Vị trí logo:", style="Heading.TLabel").grid(row=0, column=0, sticky=tk.W, pady=5)
        position_frame = ttk.Frame(quick_frame)
        position_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))

        self.create_position_selector(position_frame)
        
        # Process button
        process_frame = ttk.Frame(main_frame)
        process_frame.grid(row=3, column=0, columnspan=3, pady=20)

        self.process_btn = ttk.Button(process_frame, text="🚀 Bắt đầu xử lý ảnh",
                                     command=self.process_images, style="Process.TButton")
        self.process_btn.pack(pady=10)

        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # Status section
        status_frame = ttk.LabelFrame(main_frame, text="📋 Trạng thái xử lý", padding="10")
        status_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(0, weight=1)

        # Status text with scrollbar
        text_frame = ttk.Frame(status_frame)
        text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)

        self.status_text = tk.Text(text_frame, height=12, width=70, wrap=tk.WORD,
                                  font=("Consolas", 9), bg="#f8f9fa", fg="#2c3e50")
        self.status_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=self.status_text.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.status_text.configure(yscrollcommand=scrollbar.set)

        # Configure grid weights
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)

        # Initial status
        self.log("🎉 Chào mừng bạn đến với công cụ chèn logo vào ảnh!")
        self.log("📝 Hướng dẫn sử dụng:")
        self.log("   1️⃣ Chọn ảnh cần xử lý (nhiều file cùng lúc) hoặc chọn thư mục")
        self.log("   2️⃣ Chọn file logo (mặc định: logo.png)")
        self.log("   3️⃣ Chọn vị trí đặt logo trên lưới 3x3")
        self.log("   4️⃣ Chọn thư mục lưu kết quả (tùy chọn)")
        self.log("   5️⃣ Nhấn 'Bắt đầu xử lý ảnh'")
        self.log("📍 Vị trí mặc định: Góc dưới phải")
        self.log("✨ Sẵn sàng xử lý!")

        # Update images display
        self.update_images_display()

    def create_position_selector(self, parent):
        """Create visual position selector"""
        # Create 3x3 grid of buttons for position selection
        positions = [
            ('top-left', '↖️', 0, 0), ('top-center', '⬆️', 0, 1), ('top-right', '↗️', 0, 2),
            ('center-left', '⬅️', 1, 0), ('center', '⭕', 1, 1), ('center-right', '➡️', 1, 2),
            ('bottom-left', '↙️', 2, 0), ('bottom-center', '⬇️', 2, 1), ('bottom-right', '↘️', 2, 2)
        ]

        self.position_buttons = {}

        for pos_key, emoji, row, col in positions:
            btn = tk.Button(parent, text=emoji, width=4, height=2,
                           command=lambda p=pos_key: self.select_position(p),
                           font=("Arial", 12), relief=tk.RAISED, bd=2)
            btn.grid(row=row, column=col, padx=2, pady=2)
            self.position_buttons[pos_key] = btn

        # Set default selection (without logging)
        self.position.set('bottom-right')
        self.position_buttons['bottom-right'].configure(bg='#3498db', relief=tk.SUNKEN)

    def select_position(self, position):
        """Select logo position"""
        # Reset all buttons
        for btn in self.position_buttons.values():
            btn.configure(bg='SystemButtonFace', relief=tk.RAISED)

        # Highlight selected button
        self.position_buttons[position].configure(bg='#3498db', relief=tk.SUNKEN)
        self.position.set(position)

        position_names = {
            'top-left': 'Góc trên trái', 'top-center': 'Giữa trên', 'top-right': 'Góc trên phải',
            'center-left': 'Giữa trái', 'center': 'Chính giữa', 'center-right': 'Giữa phải',
            'bottom-left': 'Góc dưới trái', 'bottom-center': 'Giữa dưới', 'bottom-right': 'Góc dưới phải'
        }

        self.log(f"📍 Đã chọn vị trí: {position_names.get(position, position)}")

    def update_images_display(self):
        """Update the images display text"""
        self.images_text.delete(1.0, tk.END)

        if not self.selected_images:
            self.images_text.insert(tk.END, "Chưa chọn ảnh nào. Nhấn 'Chọn ảnh' hoặc 'Chọn thư mục' để bắt đầu.")
        else:
            self.images_text.insert(tk.END, f"Đã chọn {len(self.selected_images)} ảnh:\n\n")
            for i, img_path in enumerate(self.selected_images, 1):
                filename = Path(img_path).name
                self.images_text.insert(tk.END, f"{i:2d}. {filename}\n")

    def select_images(self):
        """Select multiple image files"""
        files = filedialog.askopenfilenames(
            title="Chọn ảnh cần xử lý (có thể chọn nhiều)",
            filetypes=[
                ("Tất cả ảnh", "*.jpg *.jpeg *.png *.bmp *.tiff *.webp"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("PNG files", "*.png"),
                ("BMP files", "*.bmp"),
                ("TIFF files", "*.tiff"),
                ("WEBP files", "*.webp"),
                ("Tất cả files", "*.*")
            ]
        )

        if files:
            # Add new files to existing selection (avoid duplicates)
            for file in files:
                if file not in self.selected_images:
                    self.selected_images.append(file)

            self.update_images_display()
            self.log(f"🖼️ Đã thêm {len(files)} ảnh. Tổng cộng: {len(self.selected_images)} ảnh")

    def select_folder(self):
        """Select folder and add all images from it"""
        folder = filedialog.askdirectory(title="Chọn thư mục chứa ảnh")
        if folder:
            folder_path = Path(folder)

            # Find all image files in folder
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
            new_images = []

            for ext in image_extensions:
                new_images.extend(folder_path.glob(f"*{ext}"))
                new_images.extend(folder_path.glob(f"*{ext.upper()}"))

            if new_images:
                # Add new images to selection (avoid duplicates)
                added_count = 0
                for img_path in new_images:
                    img_str = str(img_path)
                    if img_str not in self.selected_images:
                        self.selected_images.append(img_str)
                        added_count += 1

                self.update_images_display()
                self.log(f"📂 Đã thêm {added_count} ảnh từ thư mục: {folder}")
                self.log(f"📸 Tổng cộng: {len(self.selected_images)} ảnh")
            else:
                self.log(f"❌ Không tìm thấy ảnh nào trong thư mục: {folder}")

    def clear_images(self):
        """Clear all selected images"""
        self.selected_images.clear()
        self.update_images_display()
        self.log("🗑️ Đã xóa tất cả ảnh đã chọn")

    def setup_settings_tab(self, parent):
        """Setup advanced settings tab"""
        settings_frame = ttk.Frame(parent, padding="20")
        settings_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        ttk.Label(settings_frame, text="⚙️ Cài đặt nâng cao", style="Title.TLabel").pack(pady=(0, 30))

        # Logo settings
        logo_frame = ttk.LabelFrame(settings_frame, text="🖼️ Cài đặt logo", padding="15")
        logo_frame.pack(fill=tk.X, pady=(0, 20))

        # Opacity setting
        ttk.Label(logo_frame, text="Độ trong suốt:", style="Heading.TLabel").grid(row=0, column=0, sticky=tk.W, pady=5)
        opacity_frame = ttk.Frame(logo_frame)
        opacity_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))

        ttk.Scale(opacity_frame, from_=0.1, to=1.0, variable=self.opacity,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT)
        ttk.Label(opacity_frame, textvariable=self.opacity).pack(side=tk.LEFT, padx=(10, 0))

        # Logo size setting
        ttk.Label(logo_frame, text="Kích thước logo:", style="Heading.TLabel").grid(row=1, column=0, sticky=tk.W, pady=5)
        size_frame = ttk.Frame(logo_frame)
        size_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0))

        ttk.Scale(size_frame, from_=0.05, to=0.5, variable=self.logo_size,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT)
        ttk.Label(size_frame, textvariable=self.logo_size).pack(side=tk.LEFT, padx=(10, 0))

        logo_frame.columnconfigure(1, weight=1)

        # Preview section
        preview_frame = ttk.LabelFrame(settings_frame, text="👁️ Xem trước", padding="15")
        preview_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(preview_frame, text="Chức năng xem trước sẽ được thêm trong phiên bản sau",
                 font=("Arial", 10, "italic")).pack(pady=20)

    def log(self, message):
        """Add message to status text"""
        self.status_text.insert(tk.END, message + "\n")
        self.status_text.see(tk.END)
        self.root.update_idletasks()
        
    def select_logo(self):
        file = filedialog.askopenfilename(
            title="Chọn file logo",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.bmp *.tiff")]
        )
        if file:
            self.logo_path.set(file)
            try:
                # Get logo info
                with Image.open(file) as img:
                    width, height = img.size
                    self.log(f"🖼️ Đã chọn logo: {Path(file).name}")
                    self.log(f"📏 Kích thước logo: {width}x{height} pixels")
            except:
                self.log(f"🖼️ Đã chọn logo: {file}")

    def select_output_folder(self):
        folder = filedialog.askdirectory(title="Chọn thư mục lưu kết quả")
        if folder:
            self.output_folder.set(folder)
            self.log(f"💾 Đã chọn thư mục lưu: {folder}")
            
    def process_images(self):
        """Process images in a separate thread"""
        # Validation
        if not self.selected_images:
            messagebox.showerror("❌ Lỗi", "Vui lòng chọn ảnh cần xử lý!\n\nNhấn 'Chọn ảnh' hoặc 'Chọn thư mục' để bắt đầu.")
            return

        if not os.path.exists(self.logo_path.get()):
            messagebox.showerror("❌ Lỗi", f"File logo không tồn tại:\n{self.logo_path.get()}")
            return

        # Check if selected images still exist
        valid_images = []
        for img_path in self.selected_images:
            if os.path.exists(img_path):
                valid_images.append(img_path)
            else:
                self.log(f"⚠️ File không tồn tại: {Path(img_path).name}")

        if not valid_images:
            messagebox.showerror("❌ Lỗi", "Không có ảnh hợp lệ nào để xử lý!")
            return

        # Update valid images list
        self.selected_images = valid_images
        self.update_images_display()

        # Confirm processing
        result = messagebox.askyesno("🚀 Xác nhận",
                                   f"Sẵn sàng xử lý {len(self.selected_images)} ảnh?\n\n"
                                   f"📍 Vị trí: {self.position.get()}\n"
                                   f"🔍 Độ trong suốt: {self.opacity.get():.1f}\n"
                                   f"📏 Kích thước: {self.logo_size.get():.2f}")
        if not result:
            return

        self.log("\n🚀 Bắt đầu quá trình xử lý...")

        # Disable button and start progress
        self.process_btn.config(state="disabled", text="⏳ Đang xử lý...")
        self.progress.start()

        # Run in separate thread
        thread = threading.Thread(target=self._process_worker)
        thread.daemon = True
        thread.start()
        
    def _process_worker(self):
        """Worker thread for processing images"""
        try:
            self.log("\n" + "="*60)
            self.log("🚀 Bắt đầu xử lý ảnh...")
            self.log(f"📍 Vị trí logo: {self.position.get()}")
            self.log(f"🔍 Độ trong suốt: {self.opacity.get():.1f}")
            self.log(f"📏 Kích thước logo: {self.logo_size.get():.2f}")

            # Create watermark tool with custom logo size
            tool = WatermarkTool(self.logo_path.get())

            # Override resize method to use custom size
            original_resize = tool.resize_logo
            def custom_resize(logo, img_width, img_height, scale_factor=None):
                return original_resize(logo, img_width, img_height, self.logo_size.get())
            tool.resize_logo = custom_resize

            # Determine output folder
            output_folder = self.output_folder.get()
            if not output_folder:
                # Use the directory of the first image as default
                if self.selected_images:
                    first_image_dir = Path(self.selected_images[0]).parent
                    output_folder = first_image_dir / "watermarked"
                else:
                    output_folder = Path.cwd() / "watermarked"
            else:
                output_folder = Path(output_folder)

            # Create output folder
            output_folder.mkdir(exist_ok=True)
            self.log(f"📁 Thư mục lưu: {output_folder}")

            self.log(f"📸 Sẽ xử lý {len(self.selected_images)} ảnh...")

            success_count = 0
            for i, image_path in enumerate(self.selected_images, 1):
                image_file = Path(image_path)
                output_file = output_folder / f"{image_file.stem}_watermarked{image_file.suffix}"

                self.log(f"⏳ [{i}/{len(self.selected_images)}] Đang xử lý: {image_file.name}")

                if tool.add_watermark(str(image_file), str(output_file),
                                    opacity=self.opacity.get(),
                                    position=self.position.get()):
                    success_count += 1
                    self.log(f"   ✅ Thành công: {output_file.name}")
                else:
                    self.log(f"   ❌ Lỗi xử lý: {image_file.name}")

            self.log("\n" + "="*60)
            self.log(f"🎉 Hoàn thành! Đã xử lý {success_count}/{len(self.selected_images)} ảnh")
            self.log(f"📁 Kết quả lưu tại: {output_folder}")
            self.log("="*60)

            # Show completion message
            self.root.after(0, lambda: messagebox.showinfo(
                "🎉 Hoàn thành!",
                f"Đã xử lý {success_count}/{len(self.selected_images)} ảnh thành công!\n\n📁 Kết quả lưu tại:\n{output_folder}"
            ))
            
        except Exception as e:
            self.log(f"❌ Lỗi: {str(e)}")
            self.root.after(0, lambda: messagebox.showerror("Lỗi", f"Có lỗi xảy ra: {str(e)}"))
            
        finally:
            # Re-enable button and stop progress
            self.root.after(0, self._finish_processing)
            
    def _finish_processing(self):
        """Finish processing - re-enable UI"""
        self.progress.stop()
        self.process_btn.config(state="normal", text="🚀 Bắt đầu xử lý ảnh")
        self.log("✨ Sẵn sàng cho lần xử lý tiếp theo!")

def main():
    root = tk.Tk()
    app = WatermarkGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
