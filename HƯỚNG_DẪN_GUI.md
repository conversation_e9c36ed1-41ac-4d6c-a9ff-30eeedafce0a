# 🖼️ Hướng dẫn sử dụng GUI - Image Watermark Tool

## 🚀 Khởi động nhanh

1. **Nhấp đúp vào `run_gui.bat`** để mở giao diện
2. Nếu lần đầu sử dụng, tool sẽ tự động cài đặt thư viện cần thiết

## 📱 Giao diện chính

### Tab "🖼️ Xử lý ảnh"

#### 📁 Phần chọn file:
- **Ảnh cần xử lý**:
  - **🖼️ Chọn ảnh**: Chọn nhiều file ảnh cùng lúc (Ctrl+Click để chọn nhiều)
  - **📂 Chọn thư mục**: Chọn thư mục và tự động thêm tất cả ảnh trong đó
  - **🗑️ Xóa hết**: Xóa tất cả ảnh đã chọn để chọn lại
- **File logo**: Chọn logo của bạn (mặc định: logo.png)
- **Th<PERSON> mục lưu**: Chọ<PERSON> nơi lưu kết quả (t<PERSON><PERSON> ch<PERSON>, mặc định: th<PERSON> mục "watermarked")

#### ⚡ Cài đặt nhanh:
- **V<PERSON> trí logo**: Nhấn vào ô trong lưới 3x3 để chọn vị trí
  ```
  ↖️  ⬆️  ↗️     (góc trên trái, giữa trên, góc trên phải)
  ⬅️  ⭕  ➡️     (giữa trái, chính giữa, giữa phải)  
  ↙️  ⬇️  ↘️     (góc dưới trái, giữa dưới, góc dưới phải)
  ```

#### 🚀 Xử lý:
- Nhấn **"👁️ Xem trước"** để kiểm tra kết quả với ảnh đầu tiên
- Nhấn **"▶️ RUN"** để xử lý tất cả ảnh đã chọn
- Theo dõi tiến trình qua thanh progress và log

### Tab "⚙️ Cài đặt"

#### 🖼️ Cài đặt logo:
- **Độ trong suốt**: Kéo thanh từ 0.1 (mờ) đến 1.0 (đậm)
- **Kích thước logo**: Kéo thanh từ 0.05 (5%) đến 0.5 (50%) chiều rộng ảnh

#### 👁️ Xem trước:
- **Xem trước nhanh**: Nhấn nút trong tab này để xem trước
- **Cửa sổ xem trước**: Hiển thị ảnh với logo đã chèn
- **3 tùy chọn**: Tiếp tục xử lý, thay đổi cài đặt, hoặc đóng

## 💡 Mẹo sử dụng

### ✅ Chuẩn bị tốt:
- **Chọn ảnh linh hoạt**: Có thể chọn từng file hoặc cả thư mục
- **Chọn nhiều file**: Giữ Ctrl và click để chọn nhiều ảnh cùng lúc
- **Kết hợp cả hai**: Chọn file rồi chọn thêm thư mục, tool sẽ gộp tất cả
- Logo nên có nền trong suốt (file PNG) để hiệu quả tốt nhất
- Kiểm tra logo có kích thước phù hợp (không quá nhỏ hoặc quá lớn)

### 🎯 Chọn vị trí phù hợp:
- **Góc dưới phải** (↘️): Phổ biến nhất, ít che nội dung
- **Góc trên phải** (↗️): Tốt cho ảnh có nội dung ở dưới
- **Chính giữa** (⭕): Tạo hiệu ứng mạnh, dùng với độ trong suốt thấp

### ⚙️ Tùy chỉnh thông minh:
- **Ảnh sáng**: Dùng độ trong suốt 0.6-0.8
- **Ảnh tối**: Dùng độ trong suốt 0.8-1.0  
- **Logo nhỏ**: Tăng kích thước lên 0.2-0.3
- **Logo lớn**: Giảm kích thước xuống 0.1-0.15

## 🔧 Xử lý sự cố

### ❌ Lỗi thường gặp:
- **"File logo không tồn tại"**: Kiểm tra đường dẫn logo
- **"Không tìm thấy ảnh"**: Kiểm tra thư mục có chứa ảnh JPG/PNG/...
- **"Python không tìm thấy"**: Cài Python từ python.org

### 🚨 Nếu GUI không mở:
1. Mở Command Prompt
2. Gõ: `cd "đường_dẫn_thư_mục_tool"`
3. Gõ: `python watermark_gui.py`
4. Xem thông báo lỗi để khắc phục

## 📊 Định dạng hỗ trợ

### ✅ Ảnh đầu vào:
- JPG, JPEG
- PNG  
- BMP
- TIFF
- WEBP

### 💾 Ảnh đầu ra:
- Giữ nguyên định dạng gốc
- Chất lượng cao (95%)
- Tên file thêm "_watermarked"

## 🎉 Hoàn thành!

Sau khi xử lý xong:
- Kiểm tra thư mục kết quả
- Ảnh gốc vẫn được giữ nguyên
- Có thể xử lý lại với cài đặt khác nếu cần
