# 🖼️ Hướng dẫn sử dụng GUI - Image Watermark Tool

## 🚀 Khởi động nhanh

1. **Nhấp đúp vào `run_gui.bat`** để mở giao diện
2. Nếu lần đầu sử dụng, tool sẽ tự động cài đặt thư viện cần thiết

## 📱 Giao diện chính

### Tab "🖼️ Xử lý ảnh"

#### 📁 Phần chọn file:
- **Th<PERSON> mục ảnh**: Chọn thư mục chứa ảnh cần xử lý
- **File logo**: Chọn logo của bạn (mặc định: logo.png)  
- **Thư mục lưu**: Chọn nơi lưu kết quả (tù<PERSON> chọn, mặc định: thư mục "watermarked")

#### ⚡ Cài đặt nhanh:
- **Vị trí logo**: Nhấn vào ô trong lưới 3x3 để chọn vị trí
  ```
  ↖️  ⬆️  ↗️     (g<PERSON><PERSON> trên trái, gi<PERSON><PERSON> trên, g<PERSON><PERSON> trê<PERSON>hả<PERSON>)
  ⬅️  ⭕  ➡️     (g<PERSON><PERSON><PERSON> tr<PERSON><PERSON>, ch<PERSON><PERSON> g<PERSON>, gi<PERSON><PERSON> phải)  
  ↙️  ⬇️  ↘️     (góc dưới trái, giữa dưới, góc dưới phải)
  ```

#### 🚀 Xử lý:
- Nhấn **"Bắt đầu xử lý ảnh"** để bắt đầu
- Theo dõi tiến trình qua thanh progress và log

### Tab "⚙️ Cài đặt"

#### 🖼️ Cài đặt logo:
- **Độ trong suốt**: Kéo thanh từ 0.1 (mờ) đến 1.0 (đậm)
- **Kích thước logo**: Kéo thanh từ 0.05 (5%) đến 0.5 (50%) chiều rộng ảnh

## 💡 Mẹo sử dụng

### ✅ Chuẩn bị tốt:
- Đặt tất cả ảnh cần xử lý trong 1 thư mục
- Logo nên có nền trong suốt (file PNG) để hiệu quả tốt nhất
- Kiểm tra logo có kích thước phù hợp (không quá nhỏ hoặc quá lớn)

### 🎯 Chọn vị trí phù hợp:
- **Góc dưới phải** (↘️): Phổ biến nhất, ít che nội dung
- **Góc trên phải** (↗️): Tốt cho ảnh có nội dung ở dưới
- **Chính giữa** (⭕): Tạo hiệu ứng mạnh, dùng với độ trong suốt thấp

### ⚙️ Tùy chỉnh thông minh:
- **Ảnh sáng**: Dùng độ trong suốt 0.6-0.8
- **Ảnh tối**: Dùng độ trong suốt 0.8-1.0  
- **Logo nhỏ**: Tăng kích thước lên 0.2-0.3
- **Logo lớn**: Giảm kích thước xuống 0.1-0.15

## 🔧 Xử lý sự cố

### ❌ Lỗi thường gặp:
- **"File logo không tồn tại"**: Kiểm tra đường dẫn logo
- **"Không tìm thấy ảnh"**: Kiểm tra thư mục có chứa ảnh JPG/PNG/...
- **"Python không tìm thấy"**: Cài Python từ python.org

### 🚨 Nếu GUI không mở:
1. Mở Command Prompt
2. Gõ: `cd "đường_dẫn_thư_mục_tool"`
3. Gõ: `python watermark_gui.py`
4. Xem thông báo lỗi để khắc phục

## 📊 Định dạng hỗ trợ

### ✅ Ảnh đầu vào:
- JPG, JPEG
- PNG  
- BMP
- TIFF
- WEBP

### 💾 Ảnh đầu ra:
- Giữ nguyên định dạng gốc
- Chất lượng cao (95%)
- Tên file thêm "_watermarked"

## 🎉 Hoàn thành!

Sau khi xử lý xong:
- Kiểm tra thư mục kết quả
- Ảnh gốc vẫn được giữ nguyên
- Có thể xử lý lại với cài đặt khác nếu cần
