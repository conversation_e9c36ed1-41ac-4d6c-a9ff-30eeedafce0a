#!/usr/bin/env python3
"""
Test multiple file selection functionality
"""

from watermark_tool import WatermarkTool
from pathlib import Path
import os

def test_multi_files():
    """Test watermark on multiple selected files"""
    if not os.path.exists("logo.png"):
        print("Error: logo.png not found!")
        return
    
    # List of test images
    test_images = ["sample1.jpg", "sample2.jpg", "sample3.png", "sample4.jpg"]
    
    # Check which images exist
    existing_images = []
    for img in test_images:
        if os.path.exists(img):
            existing_images.append(img)
        else:
            print(f"Warning: {img} not found, skipping...")
    
    if not existing_images:
        print("Error: No test images found!")
        return
    
    print(f"Testing multi-file processing with {len(existing_images)} images...")
    
    tool = WatermarkTool("logo.png")
    
    # Create test output folder
    test_folder = Path("multi_test")
    test_folder.mkdir(exist_ok=True)
    
    success_count = 0
    
    for i, image_path in enumerate(existing_images, 1):
        image_file = Path(image_path)
        output_file = test_folder / f"{image_file.stem}_watermarked{image_file.suffix}"
        
        print(f"[{i}/{len(existing_images)}] Processing: {image_file.name}")
        
        success = tool.add_watermark(
            str(image_file), 
            str(output_file),
            opacity=0.7,
            position='bottom-right'
        )
        
        if success:
            success_count += 1
            print(f"  ✅ Success: {output_file.name}")
        else:
            print(f"  ❌ Failed: {image_file.name}")
    
    print(f"\nTest completed! Processed {success_count}/{len(existing_images)} images")
    print(f"Results saved in: {test_folder}")

if __name__ == "__main__":
    test_multi_files()
