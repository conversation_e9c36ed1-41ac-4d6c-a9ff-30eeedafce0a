#!/usr/bin/env python3
"""
Test preview functionality
"""

from watermark_tool import WatermarkTool
from pathlib import Path
import os

def test_preview():
    """Test preview creation"""
    if not os.path.exists("logo.png"):
        print("Error: logo.png not found!")
        return
    
    if not os.path.exists("sample1.jpg"):
        print("Error: sample1.jpg not found!")
        return
    
    print("Testing preview functionality...")
    
    tool = WatermarkTool("logo.png")
    
    # Test different positions for preview
    positions = ['top-left', 'center', 'bottom-right']
    
    preview_folder = Path("preview_test")
    preview_folder.mkdir(exist_ok=True)
    
    for position in positions:
        preview_file = preview_folder / f"preview_{position}.jpg"
        
        print(f"Creating preview: {position}")
        
        success = tool.add_watermark(
            "sample1.jpg",
            str(preview_file),
            opacity=0.7,
            position=position
        )
        
        if success:
            print(f"  ✅ Preview created: {preview_file.name}")
        else:
            print(f"  ❌ Failed to create preview: {position}")
    
    print(f"\nPreview test completed! Check '{preview_folder}' folder")

if __name__ == "__main__":
    test_preview()
