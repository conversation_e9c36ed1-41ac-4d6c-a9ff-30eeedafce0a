@echo off
echo Starting NEW Image Watermark Tool GUI...
echo ==========================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Check if PIL/Pillow is installed
python -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo Installing required package: Pillow...
    pip install Pillow
    if errorlevel 1 (
        echo Error: Failed to install Pillow
        pause
        exit /b 1
    )
)

echo.
echo GUI Features:
echo - Select multiple images or folders
echo - Preview before processing
echo - Clear RUN button
echo - 9 logo positions
echo.

REM Run the NEW GUI
python watermark_gui_new.py
