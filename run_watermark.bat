@echo off
echo Simple Image Watermark Tool
echo ===========================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Check if PIL/Pillow is installed
python -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo Installing required package: Pillow...
    pip install Pillow
    if errorlevel 1 (
        echo Error: Failed to install Pillow
        pause
        exit /b 1
    )
)

REM Check if logo.png exists
if not exist "logo.png" (
    echo Error: logo.png not found in current directory!
    echo Please place your logo file as "logo.png" in this folder
    pause
    exit /b 1
)

REM Run the watermark tool
echo Processing images in current directory...
python watermark_tool.py

echo.
echo Done! Check the 'watermarked' folder for results.
pause
