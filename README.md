# Simple Image Watermark Tool

Công cụ đơn giản để thêm logo watermark vào hàng loạt ảnh.

## Cách sử dụng đơn giản nhất:

1. **Chuẩn bị:**
   - Đặt file logo của bạn với tên `logo.png` trong thư mục này
   - Đặt tất cả ảnh cần watermark vào thư mục này

2. **Chạy công cụ:**
   - Nhấp đúp vào `run_watermark.bat`
   - Hoặc mở Command Prompt và gõ: `python watermark_tool.py`

3. **Kết quả:**
   - Ảnh đã watermark sẽ được lưu trong thư mục `watermarked`
   - Tên file sẽ có thêm `_watermarked` (ví dụ: `photo.jpg` → `photo_watermarked.jpg`)

## Tính năng:

- ✅ Hỗ trợ nhiều định dạng: JPG, PNG, BMP, TIFF, WEBP
- ✅ Logo tự động resize theo tỷ lệ ảnh (15% chiều rộng)
- ✅ Đặt logo ở góc dưới bên phải
- ✅ Độ trong suốt 70% (c<PERSON> thể tùy chỉnh)
- ✅ Margin 20px từ cạnh
- ✅ Xử lý hàng loạt tự động
- ✅ Không ghi đè ảnh gốc

## Sử dụng nâng cao:

```bash
# Xử lý ảnh từ thư mục khác
python watermark_tool.py "C:\path\to\images"

# Chỉ định thư mục output
python watermark_tool.py "C:\path\to\images" -o "C:\path\to\output"

# Sử dụng logo khác
python watermark_tool.py "C:\path\to\images" -l "my_logo.png"
```

## Yêu cầu hệ thống:

- Python 3.6+
- Pillow (PIL) - sẽ tự động cài đặt khi chạy batch file

## Lỗi thường gặp:

- **"logo.png not found"**: Đặt file logo với đúng tên `logo.png`
- **"No supported image files found"**: Kiểm tra định dạng ảnh có được hỗ trợ không
- **Python không tìm thấy**: Cài đặt Python từ https://python.org
