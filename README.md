# 🖼️ Image Watermark Tool - Công cụ chèn logo vào ảnh

Công cụ đơn giản và mạnh mẽ để thêm logo watermark vào hàng loạt ảnh với giao diện đồ họa thân thiện.

## 🚀 Cách sử dụng siêu đơn giản:

### Phương pháp 1: <PERSON><PERSON><PERSON> di<PERSON> đồ họa (Khuyến nghị)
1. **Nhấp đúp vào `run_gui.bat`** - Mở giao diện đồ họa
2. **Chọn ảnh cần xử lý** - Có 2 cách:
   - **🖼️ Chọn ảnh**: Chọn nhiều file ảnh cùng lúc (Ctrl+Click)
   - **📂 Chọn thư mục**: Chọn thư mục và tự động thêm tất cả ảnh
3. **Chọn file logo** - Chọn logo của bạn (mặc định: logo.png)
4. **Chọn vị trí logo** - Nhấn vào vị trí mong muốn trên lưới 3x3
5. **Tùy chỉnh cài đặt** - <PERSON><PERSON><PERSON><PERSON> chỉnh độ trong suốt, k<PERSON>ch thước (tab Cài đặt)
6. **Nhấn "Bắt đầu xử lý ảnh"** - Chờ kết quả

### Phương pháp 2: Command Line (Nhanh)
1. **Chuẩn bị:**
   - Đặt file logo với tên `logo.png` trong thư mục này
   - Đặt ảnh cần watermark vào thư mục này
2. **Nhấp đúp `run_watermark.bat`** hoặc gõ: `python watermark_tool.py`
3. **Kết quả:** Ảnh được lưu trong thư mục `watermarked`

## ✨ Tính năng nổi bật:

### 🖼️ Xử lý ảnh linh hoạt
- ✅ **Chọn file linh hoạt**: Chọn nhiều file ảnh cùng lúc hoặc cả thư mục
- ✅ **Hỗ trợ nhiều định dạng**: JPG, PNG, BMP, TIFF, WEBP
- ✅ **Xử lý hàng loạt**: Tự động xử lý tất cả ảnh đã chọn
- ✅ **An toàn**: Không ghi đè ảnh gốc
- ✅ **Tên file rõ ràng**: Thêm "_watermarked" vào tên file

### 🎯 Vị trí logo linh hoạt
- ✅ **9 vị trí khác nhau**: Góc trên trái/phải, giữa trên, giữa trái/phải/chính giữa, góc dưới trái/phải, giữa dưới
- ✅ **Giao diện trực quan**: Chọn vị trí bằng cách nhấn vào lưới 3x3
- ✅ **Xem trước vị trí**: Biểu tượng emoji hiển thị rõ ràng

### ⚙️ Tùy chỉnh nâng cao
- ✅ **Độ trong suốt**: 10% - 100% (mặc định 70%)
- ✅ **Kích thước logo**: 5% - 50% chiều rộng ảnh (mặc định 15%)
- ✅ **Margin tự động**: 20px từ cạnh ảnh

### 🎨 Giao diện thân thiện
- ✅ **GUI đẹp mắt**: Giao diện đồ họa với emoji và màu sắc
- ✅ **2 tab riêng biệt**: Xử lý ảnh và Cài đặt nâng cao
- ✅ **Theo dõi tiến trình**: Thanh progress và log chi tiết
- ✅ **Validation thông minh**: Kiểm tra file và thư mục trước khi xử lý

## Sử dụng nâng cao:

```bash
# Xử lý ảnh từ thư mục khác
python watermark_tool.py "C:\path\to\images"

# Chỉ định thư mục output
python watermark_tool.py "C:\path\to\images" -o "C:\path\to\output"

# Sử dụng logo khác
python watermark_tool.py "C:\path\to\images" -l "my_logo.png"
```

## Yêu cầu hệ thống:

- Python 3.6+
- Pillow (PIL) - sẽ tự động cài đặt khi chạy batch file

## Lỗi thường gặp:

- **"logo.png not found"**: Đặt file logo với đúng tên `logo.png`
- **"No supported image files found"**: Kiểm tra định dạng ảnh có được hỗ trợ không
- **Python không tìm thấy**: Cài đặt Python từ https://python.org
