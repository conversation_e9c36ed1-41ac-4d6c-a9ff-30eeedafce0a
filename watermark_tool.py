#!/usr/bin/env python3
"""
Simple Image Watermark Tool
Adds logo.png as watermark to all images in a folder
"""

import os
import sys
from PIL import Image, ImageEnhance
import argparse
from pathlib import Path

class WatermarkTool:
    def __init__(self, logo_path="logo.png"):
        self.logo_path = logo_path
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        
    def load_logo(self):
        """Load and prepare the logo image"""
        try:
            logo = Image.open(self.logo_path)
            # Convert to RGBA to handle transparency
            if logo.mode != 'RGBA':
                logo = logo.convert('RGBA')
            return logo
        except Exception as e:
            print(f"Error loading logo '{self.logo_path}': {e}")
            return None
    
    def resize_logo(self, logo, image_width, image_height, scale_factor=0.15):
        """Resize logo to be proportional to the image size"""
        # Calculate new logo size (15% of image width by default)
        new_width = int(image_width * scale_factor)
        
        # Maintain aspect ratio
        aspect_ratio = logo.height / logo.width
        new_height = int(new_width * aspect_ratio)
        
        return logo.resize((new_width, new_height), Image.Resampling.LANCZOS)

    def calculate_position(self, img_width, img_height, logo_width, logo_height, position, margin):
        """Calculate logo position based on selection"""
        positions = {
            'top-left': (margin, margin),
            'top-center': ((img_width - logo_width) // 2, margin),
            'top-right': (img_width - logo_width - margin, margin),
            'center-left': (margin, (img_height - logo_height) // 2),
            'center': ((img_width - logo_width) // 2, (img_height - logo_height) // 2),
            'center-right': (img_width - logo_width - margin, (img_height - logo_height) // 2),
            'bottom-left': (margin, img_height - logo_height - margin),
            'bottom-center': ((img_width - logo_width) // 2, img_height - logo_height - margin),
            'bottom-right': (img_width - logo_width - margin, img_height - logo_height - margin)
        }

        return positions.get(position, positions['bottom-right'])
    
    def add_watermark(self, image_path, output_path, opacity=0.7, margin=20, position='bottom-right'):
        """Add watermark to a single image"""
        try:
            # Load the main image
            image = Image.open(image_path)

            # Convert to RGBA for transparency support
            if image.mode != 'RGBA':
                image = image.convert('RGBA')

            # Load and resize logo
            logo = self.load_logo()
            if logo is None:
                return False

            logo_resized = self.resize_logo(logo, image.width, image.height)

            # Adjust logo opacity
            if opacity < 1.0:
                alpha = logo_resized.split()[-1]  # Get alpha channel
                alpha = ImageEnhance.Brightness(alpha).enhance(opacity)
                logo_resized.putalpha(alpha)

            # Calculate position based on selection
            x, y = self.calculate_position(image.width, image.height,
                                         logo_resized.width, logo_resized.height,
                                         position, margin)
            
            # Create a transparent overlay
            overlay = Image.new('RGBA', image.size, (0, 0, 0, 0))
            overlay.paste(logo_resized, (x, y), logo_resized)
            
            # Composite the images
            watermarked = Image.alpha_composite(image, overlay)
            
            # Convert back to RGB if saving as JPEG
            if output_path.lower().endswith(('.jpg', '.jpeg')):
                watermarked = watermarked.convert('RGB')
            
            # Save the result
            watermarked.save(output_path, quality=95)
            return True
            
        except Exception as e:
            print(f"Error processing {image_path}: {e}")
            return False
    
    def process_folder(self, input_folder, output_folder=None, position='bottom-right', opacity=0.7):
        """Process all images in a folder"""
        input_path = Path(input_folder)
        
        if not input_path.exists():
            print(f"Input folder '{input_folder}' does not exist!")
            return
        
        # Create output folder if not specified
        if output_folder is None:
            output_folder = input_path / "watermarked"
        else:
            output_folder = Path(output_folder)
        
        output_folder.mkdir(exist_ok=True)
        
        # Find all image files
        image_files = []
        for ext in self.supported_formats:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            print(f"No supported image files found in '{input_folder}'")
            print(f"Supported formats: {', '.join(self.supported_formats)}")
            return
        
        print(f"Found {len(image_files)} image(s) to process...")
        
        success_count = 0
        for image_file in image_files:
            # Create output filename
            output_file = output_folder / f"{image_file.stem}_watermarked{image_file.suffix}"
            
            print(f"Processing: {image_file.name} -> {output_file.name}")

            if self.add_watermark(str(image_file), str(output_file), opacity=opacity, position=position):
                success_count += 1
            else:
                print(f"Failed to process: {image_file.name}")
        
        print(f"\nCompleted! Successfully processed {success_count}/{len(image_files)} images")
        print(f"Output folder: {output_folder}")

def main():
    parser = argparse.ArgumentParser(description="Add watermark logo to images")
    parser.add_argument("input_folder", help="Folder containing images to watermark")
    parser.add_argument("-o", "--output", help="Output folder (default: input_folder/watermarked)")
    parser.add_argument("-l", "--logo", default="logo.png", help="Logo file path (default: logo.png)")
    
    args = parser.parse_args()
    
    # Check if logo exists
    if not os.path.exists(args.logo):
        print(f"Logo file '{args.logo}' not found!")
        sys.exit(1)
    
    # Create watermark tool and process
    tool = WatermarkTool(args.logo)
    tool.process_folder(args.input_folder, args.output)

if __name__ == "__main__":
    # If no arguments provided, use current directory
    if len(sys.argv) == 1:
        print("Simple Image Watermark Tool")
        print("=" * 30)
        
        current_dir = "."
        tool = WatermarkTool()
        
        if not os.path.exists("logo.png"):
            print("Error: logo.png not found in current directory!")
            sys.exit(1)
        
        print(f"Processing images in current directory...")
        tool.process_folder(current_dir)
    else:
        main()
